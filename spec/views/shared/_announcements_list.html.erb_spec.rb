# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

require "spec_helper"

describe "shared/_announcements_list" do
  include Factories

  before do
    @course = course_factory(active_all: true)
    @teacher = user_factory(active_all: true)
    @course.enroll_teacher(@teacher, enrollment_state: 'active')
    @user = @teacher
  end

  it "renders course announcements without priority" do
    # Create a course announcement (which doesn't have priority field)
    announcement = @course.announcements.create!(
      title: "Course Announcement",
      message: "This is a course announcement",
      user: @teacher
    )

    assign(:course_announcements, [announcement])
    assign(:global_announcements, [])
    assign(:user, @user)

    render partial: "shared/announcements_list"

    # Should show the announcement title
    expect(rendered).to include("Course Announcement")
    # Should NOT show priority badge since course announcements don't have priority
    expect(rendered).not_to include("priority-badge")
    expect(rendered).not_to match(/Priority \d+/)  # Should not show "Priority X" pattern
  end

  it "renders account notifications with priority when priority is set" do
    # Create an account notification with priority
    account_notification = AccountNotification.create!(
      account: Account.default,
      user: @teacher,
      subject: "Global Announcement",
      message: "This is a global announcement",
      start_at: 1.day.ago,
      end_at: 1.day.from_now,
      priority: 3,
      category: "Urgent"
    )
    
    assign(:course_announcements, [])
    assign(:global_announcements, [account_notification])
    assign(:user, @user)
    
    render partial: "shared/announcements_list"
    
    # Should show the announcement title
    expect(rendered).to include("Global Announcement")
    # Should show priority badge since account notification has priority
    expect(rendered).to include("Priority 3")
    expect(rendered).to include("priority-badge")
    # Should show category
    expect(rendered).to include("Urgent")
  end

  it "renders account notifications without priority when priority is not set" do
    # Create an account notification without priority
    account_notification = AccountNotification.create!(
      account: Account.default,
      user: @teacher,
      subject: "Global Announcement No Priority",
      message: "This is a global announcement without priority",
      start_at: 1.day.ago,
      end_at: 1.day.from_now,
      priority: nil,
      category: nil
    )

    assign(:course_announcements, [])
    assign(:global_announcements, [account_notification])
    assign(:user, @user)

    render partial: "shared/announcements_list"

    # Should show the announcement title
    expect(rendered).to include("Global Announcement No Priority")
    # Should NOT show priority badge since priority is nil
    expect(rendered).not_to include("priority-badge")
    expect(rendered).not_to match(/Priority \d+/)  # Should not show "Priority X" pattern
    # Should NOT show category since category is nil
    expect(rendered).not_to include("category-label")
  end

  it "renders course announcements with category when category is set" do
    # Create a course announcement with category
    announcement = @course.announcements.create!(
      title: "Course Announcement with Category",
      message: "This is a course announcement with category",
      user: @teacher,
      category: "Event"
    )

    assign(:course_announcements, [announcement])
    assign(:global_announcements, [])
    assign(:user, @user)

    render partial: "shared/announcements_list"

    # Should show the announcement title
    expect(rendered).to include("Course Announcement with Category")
    # Should show category
    expect(rendered).to include("Event")
    expect(rendered).to include("category-label")
    # Should show "(Low Priority)" since it's not urgent but has a category
    expect(rendered).to include("(Low Priority)")
    # Should NOT show priority badge since course announcements don't have priority
    expect(rendered).not_to include("priority-badge")
    expect(rendered).not_to match(/Priority \d+/)  # Should not show "Priority X" pattern
  end

  it "renders no announcements message when no announcements exist" do
    assign(:course_announcements, [])
    assign(:global_announcements, [])
    assign(:user, @user)
    
    render partial: "shared/announcements_list"
    
    expect(rendered).to include("No announcements at this time")
  end
end
