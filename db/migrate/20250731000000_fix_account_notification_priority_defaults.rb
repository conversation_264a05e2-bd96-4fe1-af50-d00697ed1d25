# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

class FixAccountNotificationPriorityDefaults < ActiveRecord::Migration[7.1]
  tag :postdeploy

  def up
    # Set priority to null for account notifications that have the default value of 0
    # but were created without explicitly setting a priority
    AccountNotification.where(priority: 0).update_all(priority: nil)
  end

  def down
    # Restore the default value of 0 for null priorities
    AccountNotification.where(priority: nil).update_all(priority: 0)
  end
end
